###
LLM翻译辅助库
提供LLM翻译相关的工具函数，包括模板验证、提示词构建、模板选择等功能

Create date:    2025-07-14
Author:         <PERSON><PERSON>
###

debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()

# 语言代码与显示名称的映射
LANGUAGE_MAP = {
  'en': 'English'
  'zh-cn': 'Chinese'
  'zh': 'Traditional Chinese'
  'kr': 'Korean'
}

###*
 * 验证提示词模板结构
 * @param {Object} template - 模板对象
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
###
module.exports.validateTemplateStructure = (template) ->
  errors = []
  
  # 检查必填字段
  requiredFields = ['_id', 'nm', 'ver', 'status', 'scenario', 'm_cfg', 'tpl', 'ts', '_mt']
  for field in requiredFields
    unless template[field]?
      errors.push "缺少必填字段: #{field}"
  
  # 检查模型配置
  if template.m_cfg?
    unless template.m_cfg.m_nm?
      errors.push "缺少模型名称: m_cfg.m_nm"
    
    supportedModels = ['gpt', 'claude', 'gemini', 'deepseek', 'grok']
    if template.m_cfg.m_nm and template.m_cfg.m_nm not in supportedModels
      errors.push "不支持的模型: #{template.m_cfg.m_nm}"
  
  # 检查模板内容
  if template.tpl?
    unless template.tpl.main?
      errors.push "缺少主要模板内容: tpl.main"
  
  # 检查变量定义
  if template.vars? and Array.isArray(template.vars)
    for variable, index in template.vars
      unless variable.nm?
        errors.push "变量 #{index} 缺少名称字段"
      unless variable.tp?
        errors.push "变量 #{index} 缺少类型字段"
      unless variable.req?
        errors.push "变量 #{index} 缺少必填标识"
  
  return {
    valid: errors.length is 0
    errors: errors
  }

###*
 * 根据内容特征选择合适的提示词模板
 * @param {String} content - 待翻译内容
 * @param {String} scenario - 翻译场景 ('ui_translation', 'forum_translation', 'comment_translation', 'property_translation', 'universal_translation')
 * @param {Array} promptTemplates - 可用的提示词模板列表
 * @returns {Object|null} 选中的模板对象
###
module.exports.selectPromptTemplate = (content, scenario, promptTemplates) ->
  return null unless content and promptTemplates and Array.isArray(promptTemplates)
  
  # 过滤出指定场景的活跃模板
  candidateTemplates = promptTemplates.filter (template) ->
    template.status is 'active' and template.scenario is scenario
  
  return null if candidateTemplates.length is 0
  
  # 如果只有一个候选模板，直接返回
  return candidateTemplates[0] if candidateTemplates.length is 1
  
  # 根据内容长度和特征选择最合适的模板
  contentLength = content.length
  
  # 优先选择GPT模型的模板（作为默认选择）
  gptTemplate = candidateTemplates.find (template) -> template.m_cfg?.m_nm is 'gpt'
  return gptTemplate if gptTemplate
  
  # 如果没有GPT模板，返回第一个可用模板
  return candidateTemplates[0]

###*
 * 构建完整的翻译提示词
 * @param {Object} template - 提示词模板
 * @param {Object} variables - 变量值对象
 * @returns {Object} 构建结果 {success: boolean, prompt: string, systemPrompt: string, error: string}
###
module.exports.buildTranslationPrompt = buildTranslationPrompt = (template, variables) ->
  return {success: false, error: '模板不能为空'} unless template
  return {success: false, error: '变量不能为空'} unless variables
  
  # 检查必填变量
  if template.vars? and Array.isArray(template.vars)
    for variable in template.vars
      if variable.req and not variables[variable.nm]?
        return {success: false, error: "缺少必填变量: #{variable.nm}"}
  
  # 替换主要提示词中的变量
  mainPrompt = template.tpl.main
  for varName, varValue of variables
    placeholder = "{#{varName}}"
    mainPrompt = mainPrompt.replace(new RegExp(placeholder, 'g'), varValue or '')
  
  # 获取系统提示词
  systemPrompt = template.tpl.sys or ''
  
  return {
    success: true
    prompt: mainPrompt
    systemPrompt: systemPrompt
    modelConfig: template.m_cfg
  }

###*
 * 根据过滤场景选择合适的过滤模板
 * @param {String} filterType - 过滤类型 ('comment_filter', 'image_filter')
 * @param {Array} promptTemplates - 可用的提示词模板列表
 * @returns {Object|null} 选中的过滤模板对象
###
module.exports.selectFilterTemplate = (filterType, promptTemplates) ->
  return null unless filterType and promptTemplates and Array.isArray(promptTemplates)
  
  # 过滤出指定过滤类型的活跃模板
  candidateTemplates = promptTemplates.filter (template) ->
    template.status is 'active' and template.scenario is filterType
  
  return null if candidateTemplates.length is 0
  
  # 优先选择GPT模型的模板
  gptTemplate = candidateTemplates.find (template) -> template.m_cfg?.m_nm is 'gpt'
  return gptTemplate if gptTemplate
  
  # 如果没有GPT模板，返回第一个可用模板
  return candidateTemplates[0]

###*
 * 构建过滤提示词
 * @param {Object} template - 过滤模板
 * @param {Object} variables - 变量值对象
 * @returns {Object} 构建结果 {success: boolean, prompt: string, systemPrompt: string, error: string}
###
module.exports.buildFilterPrompt = (template, variables) ->
  # 复用翻译提示词构建逻辑
  return buildTranslationPrompt(template, variables)

###*
 * 解析过滤结果
 * @param {String} filterResult - AI模型返回的过滤结果
 * @returns {Object} 解析结果 {passed: boolean, reason: string}
###
module.exports.parseFilterResult = (filterResult) ->
  return {passed: false, reason: '过滤结果为空'} unless filterResult
  
  result = filterResult.trim().toLowerCase()
  
  # 检查是否通过
  if result.startsWith('pass')
    return {passed: true, reason: '内容通过审核'}
  
  # 检查是否被拒绝
  if result.startsWith('reject')
    # 提取拒绝原因
    reasonMatch = result.match(/reject:\s*(.+)/)
    reason = if reasonMatch then reasonMatch[1] else '内容不符合社区规范'
    return {passed: false, reason: reason}
  
  # 默认情况下拒绝
  return {passed: false, reason: '无法解析过滤结果，默认拒绝'}

###*
 * 验证语言代码
 * @param {String} langCode - 语言代码
 * @returns {Boolean} 是否为有效的语言代码
###
module.exports.isValidLanguageCode = (langCode) ->
  supportedLanguages = ['en', 'zh-cn', 'zh', 'kr']
  return langCode in supportedLanguages

###*
 * 获取语言显示名称
 * @param {String} langCode - 语言代码
 * @returns {String} 语言显示名称
###
module.exports.getLanguageDisplayName = (langCode) ->
  return LANGUAGE_MAP[langCode] or langCode
