debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'
helpersFunction = require '../helpers_function'

ERR_THRESHOLD=0.2
RPS = 1
RPH = 1200
SLEEP_SECONDS = Math.max((3600 / RPH) * 1000, RPS * 1000)
API_TIMEOUT = 300000  # 5 minutes
BATCH_SIZE = 25
BATCH_INTERVAL = 1000  # 1 second

class GrokTranslator extends AITranslator
  constructor: (apiKey,endpoint,model,prompt,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    @lastCallTs = 0
    @translationQueue = []
    @processingBatch = false
    # Start batch processing
    @_startBatchProcessing()

  # Start continuous batch processing
  _startBatchProcessing: ->
    @processBatch = =>
      while true
        messages = []
        translations = []
        # debug.debug 'total queue length',@translationQueue.length
        if @translationQueue.length == 0 or @processingBatch
          await helpersFunction.sleep(BATCH_INTERVAL)
          continue
        @processingBatch = true
        @use()
        # Get a batch of items
        currentBatch = @translationQueue.slice(0, BATCH_SIZE)
        debug.info 'processBatch',currentBatch.length
        messages = currentBatch.map (item) -> item.message
        try
          # Process batch
          translations = await @_translateBatch(messages, currentBatch[0].fromLang, currentBatch[0].toLang)
          if translations.length != currentBatch.length
            throw new Error('Translation result length mismatch')
        catch error
          # On error, reject all promises in the batch
          for item in currentBatch
            item.reject(error)
        finally
          @processingBatch = false
          @release()
        # Distribute results
        successIndices = []
        for translation, idx in translations
          try
            @_validateTranslationLength(translation, currentBatch[idx].message)
            translation = translation.replace(/\n+$/, '')
            item = currentBatch[idx]
            item.resolve(translation)
            successIndices.push(idx)
            debug.debug '1 will remove',currentBatch[idx]
          catch error
            currentBatch[idx].reject(error)
        
        # Remove successfully processed items from queue
        # Process in reverse order to maintain correct indices
        for idx in successIndices.sort((a, b) -> b - a)
          debug.debug '2 will remove',@translationQueue[idx]
          @translationQueue.splice(idx, 1)
        
        debug.info 'processBatch end',@translationQueue.length

    # Start the batch processing loop
    @processBatch()

  # Handle control logic, including request intervals and error handling
  translate: (message, fromLang="English", toLang="Chinese") ->
    # Create a promise that will be resolved when the translation is complete
    new Promise (resolve, reject) =>
      @translationQueue.push({
        message,
        fromLang,
        toLang,
        resolve,
        reject
      })

  # Process a batch of translations
  _translateBatch: (messages, fromLang, toLang) ->
    # Calculate time since last call and wait if needed
    now = Date.now()
    timeSinceLastCall = now - @lastCallTs
    if timeSinceLastCall < SLEEP_SECONDS
      await helpersFunction.sleep(SLEEP_SECONDS - timeSinceLastCall)
    try
      @lastCallTs = Date.now()
      return await @_translate(messages, fromLang, toLang)
    catch error
      debug.error 'Grok Translation API Error:', {
        error,
        messageCount: messages.length,
        fromLang,
        toLang
      }
      errorStr = decodeURIComponent error.error
      if errorStr and errorStr.includes('Incorrect API key')
        err = Object.assign(new Error(), {
          name: 'authentication_error',
          error: 'Invalid Authorization'
        })
        debug.error err
        throw err
      if errorStr and errorStr.includes('Too many requests')
        debug.error 'Too many requests, waiting 5 minutes'
        await helpersFunction.sleep(5*60*1000)
        debug.info 'end waiting, retry'
        @lastCallTs = Date.now()
        return await @_translate(messages, fromLang, toLang)
      throw error

  # Prepare API request data
  _encodeData: (message, fromLang, toLang) ->
    if Array.isArray(message)
      messageStr = JSON.stringify(message)
      content = "translate from #{fromLang} to #{toLang}, return a list of translations and keep the original order. #{messageStr}, only return the result list"
    else
      content = "#{@prompt} #{fromLang} to #{toLang}: #{message}"
    
    return {
      model: @model
      messages: [{ role: "user", content }]
      stream: false
      temperature: 0
    }

  # Process API response data
  _decodeData: (ret, message) ->
    translatedContent = ret.choices[0].message.content
    
    try
      translatedArray = JSON.parse(translatedContent)
      if not Array.isArray(translatedArray) or translatedArray.length != message.length
        throw new Error("Invalid array length: expected #{message.length}, got #{translatedArray?.length}")
      return translatedArray
    catch error
      debug.error 'Failed to parse translation array:', {
        error,
        content: translatedContent
      }
      throw new Error('Failed to parse translation result')


  # Validate translation result length
  _validateTranslationLength: (translatedContent, message) ->
    threshold = Math.ceil(message.length * ERR_THRESHOLD)
    if translatedContent.length < threshold
      debug.error 'Translation result too short:', {
        originalLength: message.length,
        translatedLength: translatedContent.length,
        threshold
      }
      throw new Error('Translation result too short')
    return true

  # Make API call
  _invokeTranslateApi: (data) ->
    response = await fetch(@endpoint,
      method: 'POST'
      headers: {
        "Content-Type": "application/json"
        "Authorization": "Bearer #{@apiKey}"
      }
      body: JSON.stringify(data)
      signal: AbortSignal.timeout(API_TIMEOUT)
    )
    if not response.ok
      ret = await response.json()
      debug.error 'Grok API error response:', {
        error: ret,
        status: response.status
      }
      throw ret

    try
      return await response.json()
    catch error
      debug.error 'Failed to parse API response:', error
      throw new Error('Failed to parse API response')

  # Main translation process
  _translate: (message, fromLang, toLang) ->
    data = @_encodeData(message, fromLang, toLang)
    ret = await @_invokeTranslateApi(data)
    return @_decodeData(ret, message)

  ###*
   * 使用自定义提示词进行翻译（绕过批处理机制，直接调用API）
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    messages = []

    # 添加系统提示词（如果提供）
    if systemPrompt
      messages.push { role: "system", content: systemPrompt }

    # 添加用户提示词
    messages.push { role: "user", content: customPrompt }

    data = {
      model: @model
      messages: messages
      stream: false
    }

    try
      @use()
      # 直接调用API，不使用批处理机制
      ret = await @_invokeTranslateApi(data)

      if ret.choices and ret.choices.length > 0
        translatedContent = ret.choices[0].message.content
        translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
        return translatedContentDeleteN
      else
        throw new Error('No translation result returned')

    catch error
      debug.error "Grok Custom Prompt Translation API Error:", error.message
      throw error
    finally
      @release()

module.exports = GrokTranslator